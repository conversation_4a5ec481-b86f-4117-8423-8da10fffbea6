<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import {
    ArrowUpRight,
    Users,
    Clock,
    Target,
    ChevronRight,
    Play,
    CheckCircle,
    Sparkles,
    Zap,
    TrendingUp,
    Shield,
    Bot,
    FileText,
    Search,
    Send,
    Eye,
    Calendar,
    MapPin,
    DollarSign,
    Building,
    Star,
  } from 'lucide-svelte';

  let animationStep = $state(0);
  let jobCards = $state([
    {
      company: 'Google',
      role: 'Senior Software Engineer',
      salary: '$180K',
      location: 'Remote',
      status: 'applying',
    },
    {
      company: 'Microsoft',
      role: 'Product Manager',
      salary: '$160K',
      location: 'Seattle',
      status: 'applied',
    },
    {
      company: 'Apple',
      role: 'iOS Developer',
      salary: '$170K',
      location: 'Cupertino',
      status: 'interview',
    },
    {
      company: 'Meta',
      role: 'Data Scientist',
      salary: '$190K',
      location: 'Remote',
      status: 'offer',
    },
    {
      company: 'Netflix',
      role: 'DevOps Engineer',
      salary: '$165K',
      location: 'Los Gatos',
      status: 'applied',
    },
    {
      company: 'Spotify',
      role: 'Frontend Developer',
      salary: '$155K',
      location: 'Remote',
      status: 'applying',
    },
  ]);

  $effect(() => {
    const timer = setInterval(() => {
      animationStep = (animationStep + 1) % 4;

      // Simulate job application progress
      jobCards = jobCards.map((job, index) => {
        if (index === animationStep) {
          const statuses = ['applying', 'applied', 'interview', 'offer'];
          const currentIndex = statuses.indexOf(job.status);
          const nextIndex = (currentIndex + 1) % statuses.length;
          return { ...job, status: statuses[nextIndex] };
        }
        return job;
      });
    }, 2000);

    return () => clearInterval(timer);
  });
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }
</style>

<section class="min-h-screen bg-gray-50">
  <div class="grid min-h-screen lg:grid-cols-2">
    <!-- Left side - Content with floating elements -->
    <div class="relative flex flex-col justify-between overflow-hidden bg-white p-12 lg:p-16">
      <!-- Floating design elements -->
      <div
        class="absolute right-8 top-20 h-32 w-32 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 opacity-60 blur-xl">
      </div>
      <div
        class="absolute bottom-40 left-8 h-24 w-24 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 opacity-40 blur-lg">
      </div>
      <!-- Main content -->
      <div class="relative z-10 max-w-lg space-y-12">
        <div class="space-y-8">
          <!-- Animated badge -->
          <div
            class="group inline-flex cursor-pointer items-center gap-3 rounded-full border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-3 text-sm font-medium text-blue-700 transition-all duration-300 hover:from-blue-100 hover:to-indigo-100">
            <Sparkles class="h-4 w-4 transition-transform group-hover:rotate-12" />
            <span>AI-Powered Job Application Revolution</span>
            <ChevronRight class="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </div>

          <h1 class="text-[clamp(2.5rem,5vw,4.5rem)] font-bold leading-[1.1] text-gray-900">
            Automate Your
            <br />
            <span class="relative">
              Job Search
              <div
                class="absolute -bottom-2 left-0 h-3 w-full -skew-x-12 bg-gradient-to-r from-blue-200 to-indigo-200 opacity-70">
              </div>
            </span>
          </h1>

          <p class="text-lg leading-relaxed text-gray-600">
            Apply to hundreds of jobs automatically with our AI-powered platform. Smart matching,
            personalized applications, and real-time tracking. Land your dream job 10x faster.
          </p>
        </div>

        <!-- Enhanced key benefits with checkmarks -->
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-4 w-4 text-green-600" />
            </div>
            <span class="font-medium text-gray-700">Apply to 100+ jobs in minutes, not hours</span>
          </div>
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-4 w-4 text-blue-600" />
            </div>
            <span class="font-medium text-gray-700"
              >AI-powered resume and cover letter matching</span>
          </div>
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-4 w-4 text-purple-600" />
            </div>
            <span class="font-medium text-gray-700"
              >Real-time application tracking and analytics</span>
          </div>
        </div>

        <!-- Creative CTA section -->
        <div class="space-y-6">
          <div class="flex flex-col gap-4 sm:flex-row">
            <Button
              size="lg"
              class="group relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-4 text-base font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <div
                class="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 transition-opacity group-hover:opacity-100">
              </div>
              <span class="relative flex items-center gap-2">
                Start Free Trial
                <ArrowUpRight
                  class="h-4 w-4 transition-transform group-hover:-translate-y-1 group-hover:translate-x-1" />
              </span>
            </Button>
            <button
              class="group flex items-center gap-3 px-4 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <div
                class="flex h-12 w-12 items-center justify-center rounded-full border-2 border-gray-300 transition-all group-hover:border-blue-500 group-hover:bg-blue-50">
                <Play class="ml-0.5 h-5 w-5 group-hover:text-blue-600" />
              </div>
              <div class="text-left">
                <div class="font-medium">Watch Demo</div>
                <div class="text-xs text-gray-500">2 min overview</div>
              </div>
            </button>
          </div>

          <div class="flex items-center gap-4 text-sm text-gray-500">
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <div class="h-1 w-1 rounded-full bg-gray-300"></div>
            <span>Setup in under 2 minutes</span>
            <div class="h-1 w-1 rounded-full bg-gray-300"></div>
            <span>Cancel anytime</span>
          </div>
        </div>
      </div>

      <!-- Enhanced footer with social proof -->
      <div class="relative z-10 flex items-end justify-between pt-8">
        <div class="space-y-4">
          <div class="text-sm font-medium text-gray-900">
            Trusted by 10,000+ job seekers worldwide
          </div>
          <div class="flex items-center gap-8">
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">50K+</div>
              <div class="text-xs text-gray-500">Applications sent</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">85%</div>
              <div class="text-xs text-gray-500">Success rate</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">2.5x</div>
              <div class="text-xs text-gray-500">Faster hiring</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Creative Interactive Job Application Simulator -->
    <div
      class="relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-950 to-indigo-950">
      <!-- Dynamic background with floating particles -->
      <div class="absolute inset-0">
        <!-- Animated grid pattern -->
        <div
          class="absolute inset-0 opacity-20"
          style="background-image: linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px); background-size: 50px 50px;">
        </div>

        <!-- Floating orbs with different animations -->
        <div
          class="absolute left-20 top-20 h-4 w-4 animate-ping rounded-full bg-blue-400 opacity-75">
        </div>
        <div
          class="absolute right-32 top-40 h-3 w-3 animate-pulse rounded-full bg-indigo-400 opacity-60">
        </div>
        <div
          class="absolute bottom-32 left-16 h-2 w-2 animate-bounce rounded-full bg-purple-400 opacity-80">
        </div>
        <div
          class="absolute bottom-20 right-20 h-5 w-5 animate-ping rounded-full bg-cyan-400 opacity-50">
        </div>
        <div
          class="absolute left-8 top-1/2 h-3 w-3 animate-pulse rounded-full bg-emerald-400 opacity-70">
        </div>
      </div>

      <!-- Main content area -->
      <div class="relative flex h-full flex-col justify-center p-8 lg:p-12">
        <!-- Header with live stats -->
        <div class="mb-8">
          <div class="mb-4 flex items-center gap-3">
            <div class="h-3 w-3 animate-pulse rounded-full bg-green-400"></div>
            <span class="text-sm font-medium text-green-400">AI Agent Active</span>
            <div class="ml-auto text-xs text-blue-300">Live Demo</div>
          </div>
          <h3 class="mb-2 text-2xl font-bold text-white">Application Stream</h3>
          <p class="text-sm text-blue-200">Watch AI apply to jobs in real-time</p>
        </div>

        <!-- Interactive Job Cards Stream -->
        <div class="max-h-96 space-y-4 overflow-hidden">
          {#each jobCards.slice(0, 4) as job, index}
            <div
              class="transform transition-all duration-500 ease-in-out"
              style="transform: translateY({index * 4}px); opacity: {1 - index * 0.15};">
              <div
                class="group rounded-xl border border-white/20 bg-white/10 p-4 backdrop-blur-lg transition-all hover:bg-white/15">
                <!-- Job header -->
                <div class="mb-3 flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <div
                      class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-500">
                      <Building class="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div class="text-sm font-medium text-white">{job.company}</div>
                      <div class="text-xs text-blue-200">{job.role}</div>
                    </div>
                  </div>

                  <!-- Status indicator -->
                  <div class="flex items-center gap-2">
                    {#if job.status === 'applying'}
                      <div class="h-2 w-2 animate-pulse rounded-full bg-yellow-400"></div>
                      <span class="text-xs font-medium text-yellow-400">Applying...</span>
                    {:else if job.status === 'applied'}
                      <div class="h-2 w-2 rounded-full bg-blue-400"></div>
                      <span class="text-xs font-medium text-blue-400">Applied</span>
                    {:else if job.status === 'interview'}
                      <div class="h-2 w-2 animate-pulse rounded-full bg-purple-400"></div>
                      <span class="text-xs font-medium text-purple-400">Interview</span>
                    {:else if job.status === 'offer'}
                      <div class="h-2 w-2 animate-bounce rounded-full bg-green-400"></div>
                      <span class="text-xs font-medium text-green-400">Offer!</span>
                    {/if}
                  </div>
                </div>

                <!-- Job details -->
                <div class="flex items-center justify-between text-xs">
                  <div class="flex items-center gap-4 text-gray-300">
                    <div class="flex items-center gap-1">
                      <DollarSign class="h-3 w-3" />
                      <span>{job.salary}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <MapPin class="h-3 w-3" />
                      <span>{job.location}</span>
                    </div>
                  </div>

                  {#if job.status === 'applying'}
                    <div class="flex items-center gap-1 text-yellow-400">
                      <Send class="h-3 w-3 animate-pulse" />
                      <span>Sending...</span>
                    </div>
                  {:else if job.status === 'applied'}
                    <div class="flex items-center gap-1 text-blue-400">
                      <CheckCircle class="h-3 w-3" />
                      <span>Sent</span>
                    </div>
                  {:else if job.status === 'interview'}
                    <div class="flex items-center gap-1 text-purple-400">
                      <Calendar class="h-3 w-3" />
                      <span>Scheduled</span>
                    </div>
                  {:else if job.status === 'offer'}
                    <div class="flex items-center gap-1 text-green-400">
                      <Star class="h-3 w-3" />
                      <span>Success!</span>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>

        <!-- Bottom stats -->
        <div class="mt-8 border-t border-white/20 pt-6">
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-white">
                {jobCards.filter((j) => j.status === 'applied').length +
                  jobCards.filter((j) => j.status === 'interview').length +
                  jobCards.filter((j) => j.status === 'offer').length}
              </div>
              <div class="text-xs text-blue-200">Applied Today</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-white">
                {jobCards.filter((j) => j.status === 'interview').length +
                  jobCards.filter((j) => j.status === 'offer').length}
              </div>
              <div class="text-xs text-blue-200">Responses</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-white">
                {jobCards.filter((j) => j.status === 'offer').length}
              </div>
              <div class="text-xs text-blue-200">Offers</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
