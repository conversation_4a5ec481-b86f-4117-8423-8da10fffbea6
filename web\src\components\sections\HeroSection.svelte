<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import {
    ArrowUpRight,
    Users,
    Clock,
    Target,
    ChevronRight,
    Play,
    CheckCircle,
    Sparkles,
    Zap,
    TrendingUp,
    Shield,
    Bot,
    FileText,
    Search,
    Send,
    Eye,
    Calendar,
    MapPin,
    DollarSign,
    Star,
  } from 'lucide-svelte';

  let animationStep = $state(0);
  let currentTime = $state(new Date());
  let jobCards = $state([
    {
      id: 1,
      company: 'Google',
      role: 'Senior Software Engineer',
      salary: '$180K - $220K',
      location: 'Remote',
      status: 'applying',
      logo: '🔍',
      matchScore: 95,
      timeAgo: '2m ago',
    },
    {
      id: 2,
      company: 'Microsoft',
      role: 'Product Manager',
      salary: '$160K - $190K',
      location: 'Seattle, WA',
      status: 'applied',
      logo: '💼',
      matchScore: 88,
      timeAgo: '5m ago',
    },
    {
      id: 3,
      company: 'Apple',
      role: 'iOS Developer',
      salary: '$170K - $200K',
      location: 'Cupertino, CA',
      status: 'interview',
      logo: '📱',
      matchScore: 92,
      timeAgo: '8m ago',
    },
    {
      id: 4,
      company: 'Meta',
      role: 'Data Scientist',
      salary: '$190K - $240K',
      location: 'Remote',
      status: 'offer',
      logo: '📊',
      matchScore: 97,
      timeAgo: '12m ago',
    },
    {
      id: 5,
      company: 'Netflix',
      role: 'DevOps Engineer',
      salary: '$165K - $195K',
      location: 'Los Gatos, CA',
      status: 'applied',
      logo: '🎬',
      matchScore: 85,
      timeAgo: '15m ago',
    },
    {
      id: 6,
      company: 'Spotify',
      role: 'Frontend Developer',
      salary: '$155K - $185K',
      location: 'Remote',
      status: 'applying',
      logo: '🎵',
      matchScore: 90,
      timeAgo: '18m ago',
    },
  ]);

  $effect(() => {
    const timer = setInterval(() => {
      currentTime = new Date();
      animationStep = (animationStep + 1) % jobCards.length;

      // Simulate realistic job application progress
      jobCards = jobCards.map((job, index) => {
        if (index === animationStep) {
          const statuses = ['applying', 'applied', 'interview', 'offer'] as const;

          // More realistic progression - not all jobs get offers
          let nextIndex: number;
          if (job.status === 'applying') {
            nextIndex = 1; // applying -> applied
          } else if (job.status === 'applied') {
            nextIndex = Math.random() > 0.7 ? 2 : 1; // 30% chance for interview
          } else if (job.status === 'interview') {
            nextIndex = Math.random() > 0.6 ? 3 : 2; // 40% chance for offer
          } else {
            nextIndex = 0; // reset to applying for demo
          }

          return { ...job, status: statuses[nextIndex] };
        }
        return job;
      });
    }, 3000);

    return () => clearInterval(timer);
  });
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }
</style>

<section class="min-h-screen bg-gray-50">
  <div class="grid min-h-screen lg:grid-cols-2">
    <!-- Left side - Content with floating elements -->
    <div class="relative flex flex-col justify-between overflow-hidden bg-white p-12 lg:p-16">
      <!-- Floating design elements -->
      <div
        class="absolute right-8 top-20 h-32 w-32 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 opacity-60 blur-xl">
      </div>
      <div
        class="absolute bottom-40 left-8 h-24 w-24 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 opacity-40 blur-lg">
      </div>
      <!-- Main content -->
      <div class="relative z-10 max-w-lg space-y-12">
        <div class="space-y-8">
          <!-- Animated badge -->
          <div
            class="group inline-flex cursor-pointer items-center gap-3 rounded-full border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-3 text-sm font-medium text-blue-700 transition-all duration-300 hover:from-blue-100 hover:to-indigo-100">
            <Sparkles class="h-4 w-4 transition-transform group-hover:rotate-12" />
            <span>AI-Powered Job Application Revolution</span>
            <ChevronRight class="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </div>

          <h1 class="text-[clamp(2.5rem,5vw,4.5rem)] font-bold leading-[1.1] text-gray-900">
            Automate Your
            <br />
            <span class="relative">
              Job Search
              <div
                class="absolute -bottom-2 left-0 h-3 w-full -skew-x-12 bg-gradient-to-r from-blue-200 to-indigo-200 opacity-70">
              </div>
            </span>
          </h1>

          <p class="text-lg leading-relaxed text-gray-600">
            Apply to hundreds of jobs automatically with our AI-powered platform. Smart matching,
            personalized applications, and real-time tracking. Land your dream job 10x faster.
          </p>
        </div>

        <!-- Enhanced key benefits with checkmarks -->
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-4 w-4 text-green-600" />
            </div>
            <span class="font-medium text-gray-700">Apply to 100+ jobs in minutes, not hours</span>
          </div>
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-4 w-4 text-blue-600" />
            </div>
            <span class="font-medium text-gray-700"
              >AI-powered resume and cover letter matching</span>
          </div>
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-4 w-4 text-purple-600" />
            </div>
            <span class="font-medium text-gray-700"
              >Real-time application tracking and analytics</span>
          </div>
        </div>

        <!-- Creative CTA section -->
        <div class="space-y-6">
          <div class="flex flex-col gap-4 sm:flex-row">
            <Button
              size="lg"
              class="group relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-4 text-base font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <div
                class="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 transition-opacity group-hover:opacity-100">
              </div>
              <span class="relative flex items-center gap-2">
                Start Free Trial
                <ArrowUpRight
                  class="h-4 w-4 transition-transform group-hover:-translate-y-1 group-hover:translate-x-1" />
              </span>
            </Button>
            <button
              class="group flex items-center gap-3 px-4 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <div
                class="flex h-12 w-12 items-center justify-center rounded-full border-2 border-gray-300 transition-all group-hover:border-blue-500 group-hover:bg-blue-50">
                <Play class="ml-0.5 h-5 w-5 group-hover:text-blue-600" />
              </div>
              <div class="text-left">
                <div class="font-medium">Watch Demo</div>
                <div class="text-xs text-gray-500">2 min overview</div>
              </div>
            </button>
          </div>

          <div class="flex items-center gap-4 text-sm text-gray-500">
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <div class="h-1 w-1 rounded-full bg-gray-300"></div>
            <span>Setup in under 2 minutes</span>
            <div class="h-1 w-1 rounded-full bg-gray-300"></div>
            <span>Cancel anytime</span>
          </div>
        </div>
      </div>

      <!-- Enhanced footer with social proof -->
      <div class="relative z-10 flex items-end justify-between pt-8">
        <div class="space-y-4">
          <div class="text-sm font-medium text-gray-900">
            Trusted by 10,000+ job seekers worldwide
          </div>
          <div class="flex items-center gap-8">
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">50K+</div>
              <div class="text-xs text-gray-500">Applications sent</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">85%</div>
              <div class="text-xs text-gray-500">Success rate</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">2.5x</div>
              <div class="text-xs text-gray-500">Faster hiring</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Creative Interactive Job Application Simulator -->
    <div
      class="relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-950 to-indigo-950">
      <!-- Dynamic background with floating particles -->
      <div class="absolute inset-0">
        <!-- Animated grid pattern -->
        <div
          class="absolute inset-0 opacity-20"
          style="background-image: linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px); background-size: 50px 50px;">
        </div>

        <!-- Floating orbs with different animations -->
        <div
          class="absolute left-20 top-20 h-4 w-4 animate-ping rounded-full bg-blue-400 opacity-75">
        </div>
        <div
          class="absolute right-32 top-40 h-3 w-3 animate-pulse rounded-full bg-indigo-400 opacity-60">
        </div>
        <div
          class="absolute bottom-32 left-16 h-2 w-2 animate-bounce rounded-full bg-purple-400 opacity-80">
        </div>
        <div
          class="absolute bottom-20 right-20 h-5 w-5 animate-ping rounded-full bg-cyan-400 opacity-50">
        </div>
        <div
          class="absolute left-8 top-1/2 h-3 w-3 animate-pulse rounded-full bg-emerald-400 opacity-70">
        </div>
      </div>

      <!-- Main content area -->
      <div class="relative flex h-full flex-col justify-center p-8 lg:p-12">
        <!-- Enhanced Header with live stats -->
        <div class="mb-8">
          <div class="mb-4 flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="relative">
                <div class="h-3 w-3 animate-pulse rounded-full bg-green-400"></div>
                <div
                  class="absolute inset-0 h-3 w-3 animate-ping rounded-full bg-green-400 opacity-75">
                </div>
              </div>
              <span class="text-sm font-medium text-green-400">AI Agent Active</span>
              <div class="rounded-full bg-green-400/20 px-2 py-1 text-xs text-green-300">
                {jobCards.filter((j) => j.status === 'applying').length} processing
              </div>
            </div>
            <div class="rounded-lg bg-white/10 px-3 py-1 text-xs text-blue-300 backdrop-blur-sm">
              Live Demo
            </div>
          </div>
          <h3 class="mb-2 text-3xl font-bold text-white">Application Stream</h3>
          <p class="text-blue-200">Watch AI apply to jobs in real-time with smart matching</p>
        </div>

        <!-- Enhanced Interactive Job Cards Stream -->
        <div class="relative max-h-96 space-y-3 overflow-hidden">
          {#each jobCards.slice(0, 4) as job, index}
            <div
              class="transform transition-all duration-700 ease-out"
              style="transform: translateY({index * 2}px) scale({1 - index * 0.02}); opacity: {1 -
                index * 0.12};">
              <div
                class="group relative rounded-xl border border-white/20 bg-gradient-to-r from-white/10 to-white/5 p-4 backdrop-blur-lg transition-all duration-300 hover:border-white/30 hover:from-white/15 hover:to-white/10">
                <!-- Animated border glow for active jobs -->
                {#if job.status === 'applying'}
                  <div
                    class="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-400/20 to-orange-400/20 opacity-50 blur-sm">
                  </div>
                {/if}

                <div class="relative">
                  <!-- Job header with enhanced layout -->
                  <div class="mb-3 flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class="relative">
                        <div
                          class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 text-lg shadow-lg">
                          {job.logo}
                        </div>
                        <!-- Match score badge -->
                        <div
                          class="absolute -bottom-1 -right-1 rounded-full bg-green-500 px-1.5 py-0.5 text-xs font-bold text-white">
                          {job.matchScore}%
                        </div>
                      </div>
                      <div class="flex-1">
                        <div class="flex items-center gap-2">
                          <div class="text-sm font-semibold text-white">{job.company}</div>
                          <div class="text-xs text-gray-400">{job.timeAgo}</div>
                        </div>
                        <div class="text-xs text-blue-200">{job.role}</div>
                      </div>
                    </div>

                    <!-- Enhanced Status indicator -->
                    <div class="flex flex-col items-end gap-1">
                      <div class="flex items-center gap-2">
                        {#if job.status === 'applying'}
                          <div class="relative">
                            <div class="h-2 w-2 animate-pulse rounded-full bg-yellow-400"></div>
                            <div
                              class="absolute inset-0 h-2 w-2 animate-ping rounded-full bg-yellow-400 opacity-75">
                            </div>
                          </div>
                          <span class="text-xs font-medium text-yellow-400">Applying</span>
                        {:else if job.status === 'applied'}
                          <div class="h-2 w-2 rounded-full bg-blue-400"></div>
                          <span class="text-xs font-medium text-blue-400">Applied</span>
                        {:else if job.status === 'interview'}
                          <div class="h-2 w-2 animate-pulse rounded-full bg-purple-400"></div>
                          <span class="text-xs font-medium text-purple-400">Interview</span>
                        {:else if job.status === 'offer'}
                          <div class="h-2 w-2 animate-bounce rounded-full bg-green-400"></div>
                          <span class="text-xs font-medium text-green-400">Offer!</span>
                        {/if}
                      </div>
                    </div>
                  </div>

                  <!-- Enhanced Job details -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4 text-xs text-gray-300">
                      <div class="flex items-center gap-1">
                        <DollarSign class="h-3 w-3" />
                        <span class="font-medium">{job.salary}</span>
                      </div>
                      <div class="flex items-center gap-1">
                        <MapPin class="h-3 w-3" />
                        <span>{job.location}</span>
                      </div>
                    </div>

                    <!-- Action indicator -->
                    <div class="flex items-center gap-1 text-xs">
                      {#if job.status === 'applying'}
                        <div class="flex items-center gap-1 text-yellow-400">
                          <Send class="h-3 w-3 animate-pulse" />
                          <span class="font-medium">Processing</span>
                        </div>
                      {:else if job.status === 'applied'}
                        <div class="flex items-center gap-1 text-blue-400">
                          <CheckCircle class="h-3 w-3" />
                          <span class="font-medium">Submitted</span>
                        </div>
                      {:else if job.status === 'interview'}
                        <div class="flex items-center gap-1 text-purple-400">
                          <Calendar class="h-3 w-3" />
                          <span class="font-medium">Scheduled</span>
                        </div>
                      {:else if job.status === 'offer'}
                        <div class="flex items-center gap-1 text-green-400">
                          <Star class="h-3 w-3 animate-pulse" />
                          <span class="font-medium">Success!</span>
                        </div>
                      {/if}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {/each}

          <!-- Fade out gradient at bottom -->
          <div
            class="pointer-events-none absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-slate-900 to-transparent">
          </div>
        </div>

        <!-- Enhanced Bottom stats with better visual hierarchy -->
        <div class="mt-8 rounded-xl border border-white/20 bg-white/5 p-6 backdrop-blur-sm">
          <div class="mb-4 text-center">
            <h4 class="text-sm font-medium text-white">Today's Performance</h4>
            <p class="text-xs text-blue-300">Real-time automation metrics</p>
          </div>

          <div class="grid grid-cols-3 gap-6">
            <div class="text-center">
              <div class="mb-1 flex items-center justify-center">
                <div class="text-2xl font-bold text-white">
                  {jobCards.filter((j) => j.status === 'applied').length +
                    jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length}
                </div>
                <TrendingUp class="ml-1 h-4 w-4 text-green-400" />
              </div>
              <div class="text-xs font-medium text-blue-200">Applications Sent</div>
              <div class="text-xs text-gray-400">
                +{Math.floor(Math.random() * 5) + 2} this hour
              </div>
            </div>

            <div class="text-center">
              <div class="mb-1 flex items-center justify-center">
                <div class="text-2xl font-bold text-white">
                  {jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length}
                </div>
                <Eye class="ml-1 h-4 w-4 text-purple-400" />
              </div>
              <div class="text-xs font-medium text-blue-200">Responses</div>
              <div class="text-xs text-gray-400">{Math.floor(Math.random() * 3) + 1} pending</div>
            </div>

            <div class="text-center">
              <div class="mb-1 flex items-center justify-center">
                <div class="text-2xl font-bold text-white">
                  {jobCards.filter((j) => j.status === 'offer').length}
                </div>
                <Star class="ml-1 h-4 w-4 text-yellow-400" />
              </div>
              <div class="text-xs font-medium text-blue-200">Job Offers</div>
              <div class="text-xs text-gray-400">
                {jobCards.filter((j) => j.status === 'offer').length > 0
                  ? 'Congratulations!'
                  : 'Keep going!'}
              </div>
            </div>
          </div>

          <!-- Success rate indicator -->
          <div class="mt-4 border-t border-white/10 pt-4">
            <div class="flex items-center justify-between text-xs">
              <span class="text-blue-300">Success Rate</span>
              <span class="font-medium text-green-400">
                {Math.round(
                  ((jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length) /
                    Math.max(jobCards.filter((j) => j.status !== 'applying').length, 1)) *
                    100
                )}%
              </span>
            </div>
            <div class="mt-2 h-1.5 rounded-full bg-white/10">
              <div
                class="h-full rounded-full bg-gradient-to-r from-green-400 to-emerald-400 transition-all duration-500"
                style="width: {Math.round(
                  ((jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length) /
                    Math.max(jobCards.filter((j) => j.status !== 'applying').length, 1)) *
                    100
                )}%">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
