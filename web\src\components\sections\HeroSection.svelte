<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { ArrowUpRight, Play, CheckCircle, Bot, MapPin, DollarSign } from 'lucide-svelte';

  let streamingJobs = $state([]);
  let jobIndex = $state(0);

  const jobPool = [
    {
      company: 'Google',
      role: 'Senior Software Engineer',
      salary: '$180K - $220K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/google.com',
      matchScore: 95,
    },
    {
      company: 'Microsoft',
      role: 'Product Manager',
      salary: '$160K - $190K',
      location: 'Seattle, WA',
      logoUrl: 'https://logo.clearbit.com/microsoft.com',
      matchScore: 88,
    },
    {
      company: 'Apple',
      role: 'iOS Developer',
      salary: '$170K - $200K',
      location: 'Cupertino, CA',
      logoUrl: 'https://logo.clearbit.com/apple.com',
      matchScore: 92,
    },
    {
      company: 'Meta',
      role: 'Data Scientist',
      salary: '$190K - $240K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/meta.com',
      matchScore: 97,
    },
    {
      company: 'Netflix',
      role: 'DevOps Engineer',
      salary: '$165K - $195K',
      location: 'Los Gatos, CA',
      logoUrl: 'https://logo.clearbit.com/netflix.com',
      matchScore: 85,
    },
    {
      company: 'Spotify',
      role: 'Frontend Developer',
      salary: '$155K - $185K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/spotify.com',
      matchScore: 90,
    },
    {
      company: 'Amazon',
      role: 'Cloud Engineer',
      salary: '$175K - $210K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/amazon.com',
      matchScore: 89,
    },
    {
      company: 'Tesla',
      role: 'Software Engineer',
      salary: '$170K - $200K',
      location: 'Austin, TX',
      logoUrl: 'https://logo.clearbit.com/tesla.com',
      matchScore: 91,
    },
    {
      company: 'Stripe',
      role: 'Backend Developer',
      salary: '$185K - $220K',
      location: 'San Francisco, CA',
      logoUrl: 'https://logo.clearbit.com/stripe.com',
      matchScore: 93,
    },
    {
      company: 'Airbnb',
      role: 'Full Stack Engineer',
      salary: '$165K - $195K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/airbnb.com',
      matchScore: 87,
    },
    {
      company: 'Uber',
      role: 'Mobile Developer',
      salary: '$160K - $190K',
      location: 'San Francisco, CA',
      logoUrl: 'https://logo.clearbit.com/uber.com',
      matchScore: 86,
    },
    {
      company: 'Slack',
      role: 'Platform Engineer',
      salary: '$170K - $200K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/slack.com',
      matchScore: 88,
    },
    {
      company: 'Shopify',
      role: 'Full Stack Developer',
      salary: '$155K - $185K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/shopify.com',
      matchScore: 89,
    },
    {
      company: 'Zoom',
      role: 'Backend Engineer',
      salary: '$160K - $190K',
      location: 'San Jose, CA',
      logoUrl: 'https://logo.clearbit.com/zoom.us',
      matchScore: 84,
    },
    {
      company: 'Dropbox',
      role: 'Product Engineer',
      salary: '$175K - $205K',
      location: 'San Francisco, CA',
      logoUrl: 'https://logo.clearbit.com/dropbox.com',
      matchScore: 91,
    },
    {
      company: 'Square',
      role: 'Software Engineer',
      salary: '$165K - $195K',
      location: 'Remote',
      logoUrl: 'https://logo.clearbit.com/squareup.com',
      matchScore: 87,
    },
  ];

  // Continuous streaming animation
  $effect(() => {
    const timer = setInterval(() => {
      // Add new job at the top
      const newJob = {
        ...jobPool[jobIndex % jobPool.length],
        id: Date.now() + Math.random(),
        status: 'applying',
        animationDelay: 0,
      };

      // Keep only 8 jobs visible (4 rows x 2 columns)
      streamingJobs = [newJob, ...streamingJobs.slice(0, 7)];
      jobIndex = jobIndex + 1;
    }, 800); // New job every 800ms

    return () => clearInterval(timer);
  });
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }
</style>

<section class="min-h-screen bg-gray-50">
  <div class="grid min-h-screen lg:grid-cols-[2fr_2fr]">
    <!-- Left side - Cleaner content layout (40%) -->
    <div class="relative flex flex-col justify-center overflow-hidden bg-white p-8 lg:p-12">
      <!-- Subtle floating design elements -->
      <div
        class="absolute right-4 top-16 h-20 w-20 rounded-full bg-gradient-to-br from-blue-50 to-indigo-50 opacity-40 blur-xl">
      </div>
      <div
        class="absolute bottom-32 left-4 h-16 w-16 rounded-full bg-gradient-to-br from-purple-50 to-pink-50 opacity-30 blur-lg">
      </div>

      <!-- Main content -->
      <div class="relative z-10 max-w-md space-y-8">
        <!-- Cleaner header section -->
        <div class="space-y-6">
          <!-- Simplified badge -->
          <div
            class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700">
            <Bot class="h-4 w-4" />
            <span>AI-Powered Automation</span>
          </div>

          <h1 class="text-4xl font-bold leading-tight text-gray-900 lg:text-6xl">
            Apply to
            <span class="relative text-blue-600">
              Hundreds
              <div
                class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60">
              </div>
            </span>
            of Jobs Automatically
          </h1>

          <p class="text-lg text-gray-600">
            Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.
          </p>
        </div>

        <!-- Streamlined key benefits -->
        <div class="space-y-3">
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-3 w-3 text-green-600" />
            </div>
            <span class="text-gray-700">100+ applications in minutes</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-3 w-3 text-blue-600" />
            </div>
            <span class="text-gray-700">AI-powered resume matching</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-3 w-3 text-purple-600" />
            </div>
            <span class="text-gray-700">Real-time tracking & analytics</span>
          </div>
        </div>

        <!-- Simplified CTA section -->
        <div class="space-y-4">
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              size="lg"
              class="group rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-3 font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <span class="flex items-center gap-2">
                Start Free Trial
                <ArrowUpRight class="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
            <button
              class="group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <Play class="h-4 w-4 group-hover:text-blue-600" />
              <span>Watch Demo</span>
            </button>
          </div>

          <div class="flex items-center gap-3 text-xs text-gray-500">
            <div class="flex items-center gap-1">
              <div class="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <span>•</span>
            <span>Setup in 2 minutes</span>
          </div>
        </div>

        <!-- Compact social proof -->
        <div class="rounded-lg bg-gray-50 p-4">
          <div class="mb-2 text-sm font-medium text-gray-900">Trusted by 10,000+ job seekers</div>
          <div class="flex items-center gap-6 text-xs">
            <div>
              <span class="font-bold text-gray-900">50K+</span>
              <span class="text-gray-500"> applications</span>
            </div>
            <div>
              <span class="font-bold text-gray-900">85%</span>
              <span class="text-gray-500"> success rate</span>
            </div>
            <div>
              <span class="font-bold text-gray-900">2.5x</span>
              <span class="text-gray-500"> faster</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Clean Job Application Stream (60%) -->
    <div class="relative overflow-hidden bg-slate-900">
      <!-- Subtle background pattern -->
      <div class="absolute inset-0">
        <div
          class="absolute inset-0 opacity-5"
          style="background-image: linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px); background-size: 40px 40px;">
        </div>
      </div>

      <!-- Main content area -->
      <div class="relative flex h-full flex-col justify-center p-8 lg:p-12">
        <!-- Streaming Header -->
        <div class="mb-6">
          <div class="mb-3 flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
              <span class="text-sm text-green-400"
                >Processing {streamingJobs.length} applications</span>
            </div>
            <div class="rounded bg-white/10 px-2 py-1 text-xs text-gray-400">Live Demo</div>
          </div>
          <h3 class="text-xl font-bold text-white">Automation Stream</h3>
        </div>

        <!-- Continuous Job Stream Grid -->
        <div class="grid grid-cols-2 gap-3 overflow-hidden">
          {#each streamingJobs as job, index (job.id)}
            <div
              class="animate-in slide-in-from-top-4 fade-in rounded-lg border border-white/10 bg-white/5 p-3 duration-500"
              style="animation-delay: {index * 50}ms;">
              <!-- Company logo -->
              <div class="mb-2 flex items-center gap-2">
                <div class="h-8 w-8 overflow-hidden rounded bg-white p-1">
                  <img
                    src={job.logoUrl}
                    alt={job.company}
                    class="h-full w-full object-contain"
                    loading="lazy" />
                </div>
                <div class="min-w-0 flex-1">
                  <div class="truncate text-sm font-medium text-white">{job.company}</div>
                  <div class="truncate text-xs text-gray-400">{job.role}</div>
                </div>
              </div>

              <!-- Job details -->
              <div class="space-y-1 text-xs text-gray-300">
                <div class="flex items-center gap-1">
                  <DollarSign class="h-3 w-3" />
                  <span class="truncate">{job.salary}</span>
                </div>
                <div class="flex items-center gap-1">
                  <MapPin class="h-3 w-3" />
                  <span class="truncate">{job.location}</span>
                </div>
              </div>

              <!-- Status -->
              <div class="mt-2 flex items-center justify-between">
                <div class="flex items-center gap-1">
                  <div class="h-1.5 w-1.5 animate-pulse rounded-full bg-yellow-400"></div>
                  <span class="text-xs text-yellow-400">Processing</span>
                </div>
                <div class="rounded bg-green-500 px-1.5 py-0.5 text-xs font-bold text-white">
                  {job.matchScore}%
                </div>
              </div>
            </div>
          {/each}
        </div>

        <!-- Simple Performance Stats -->
        <div class="mt-6 rounded-lg border border-white/10 bg-white/5 p-4">
          <div class="flex items-center justify-between text-sm">
            <div class="text-center">
              <div class="text-lg font-bold text-white">
                {jobCards.filter((j) => j.status === 'applied').length +
                  jobCards.filter((j) => j.status === 'interview').length +
                  jobCards.filter((j) => j.status === 'offer').length}
              </div>
              <div class="text-xs text-gray-400">Applied</div>
            </div>

            <div class="text-center">
              <div class="text-lg font-bold text-white">
                {jobCards.filter((j) => j.status === 'interview').length +
                  jobCards.filter((j) => j.status === 'offer').length}
              </div>
              <div class="text-xs text-gray-400">Responses</div>
            </div>

            <div class="text-center">
              <div class="text-lg font-bold text-white">
                {jobCards.filter((j) => j.status === 'offer').length}
              </div>
              <div class="text-xs text-gray-400">Offers</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
