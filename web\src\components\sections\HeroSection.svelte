<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import {
    ArrowUpRight,
    Play,
    CheckCircle,
    Bot,
    TrendingUp,
    Eye,
    MapPin,
    DollarSign,
    Star,
  } from 'lucide-svelte';

  let animationStep = $state(0);
  let currentTime = $state(new Date());
  let jobCards = $state([
    {
      id: 1,
      company: 'Google',
      role: 'Senior Software Engineer',
      salary: '$180K - $220K',
      location: 'Remote',
      status: 'applying',
      logoUrl: 'https://logo.clearbit.com/google.com',
      matchScore: 95,
      timeAgo: '2m ago',
    },
    {
      id: 2,
      company: 'Microsoft',
      role: 'Product Manager',
      salary: '$160K - $190K',
      location: 'Seattle, WA',
      status: 'applied',
      logoUrl: 'https://logo.clearbit.com/microsoft.com',
      matchScore: 88,
      timeAgo: '5m ago',
    },
    {
      id: 3,
      company: 'Apple',
      role: 'iOS Developer',
      salary: '$170K - $200K',
      location: 'Cupertino, CA',
      status: 'interview',
      logoUrl: 'https://logo.clearbit.com/apple.com',
      matchScore: 92,
      timeAgo: '8m ago',
    },
    {
      id: 4,
      company: 'Meta',
      role: 'Data Scientist',
      salary: '$190K - $240K',
      location: 'Remote',
      status: 'offer',
      logoUrl: 'https://logo.clearbit.com/meta.com',
      matchScore: 97,
      timeAgo: '12m ago',
    },
    {
      id: 5,
      company: 'Netflix',
      role: 'DevOps Engineer',
      salary: '$165K - $195K',
      location: 'Los Gatos, CA',
      status: 'applied',
      logoUrl: 'https://logo.clearbit.com/netflix.com',
      matchScore: 85,
      timeAgo: '15m ago',
    },
    {
      id: 6,
      company: 'Spotify',
      role: 'Frontend Developer',
      salary: '$155K - $185K',
      location: 'Remote',
      status: 'applying',
      logoUrl: 'https://logo.clearbit.com/spotify.com',
      matchScore: 90,
      timeAgo: '18m ago',
    },
    {
      id: 7,
      company: 'Amazon',
      role: 'Cloud Engineer',
      salary: '$175K - $210K',
      location: 'Remote',
      status: 'applying',
      logoUrl: 'https://logo.clearbit.com/amazon.com',
      matchScore: 89,
      timeAgo: '20m ago',
    },
    {
      id: 8,
      company: 'Tesla',
      role: 'Software Engineer',
      salary: '$170K - $200K',
      location: 'Austin, TX',
      status: 'applied',
      logoUrl: 'https://logo.clearbit.com/tesla.com',
      matchScore: 91,
      timeAgo: '22m ago',
    },
    {
      id: 9,
      company: 'Stripe',
      role: 'Backend Developer',
      salary: '$185K - $220K',
      location: 'San Francisco, CA',
      status: 'applying',
      logoUrl: 'https://logo.clearbit.com/stripe.com',
      matchScore: 93,
      timeAgo: '25m ago',
    },
    {
      id: 10,
      company: 'Airbnb',
      role: 'Full Stack Engineer',
      salary: '$165K - $195K',
      location: 'Remote',
      status: 'applied',
      logoUrl: 'https://logo.clearbit.com/airbnb.com',
      matchScore: 87,
      timeAgo: '28m ago',
    },
    {
      id: 11,
      company: 'Uber',
      role: 'Mobile Developer',
      salary: '$160K - $190K',
      location: 'San Francisco, CA',
      status: 'applying',
      logoUrl: 'https://logo.clearbit.com/uber.com',
      matchScore: 86,
      timeAgo: '30m ago',
    },
    {
      id: 12,
      company: 'Slack',
      role: 'Platform Engineer',
      salary: '$170K - $200K',
      location: 'Remote',
      status: 'applied',
      logoUrl: 'https://logo.clearbit.com/slack.com',
      matchScore: 88,
      timeAgo: '32m ago',
    },
  ]);

  $effect(() => {
    const timer = setInterval(() => {
      currentTime = new Date();
      animationStep = (animationStep + 1) % jobCards.length;

      // Simulate realistic job application progress with multiple jobs updating
      jobCards = jobCards.map((job, index) => {
        // Update multiple jobs at once to show more activity
        const shouldUpdate =
          index === animationStep ||
          index === (animationStep + 3) % jobCards.length ||
          index === (animationStep + 6) % jobCards.length;

        if (shouldUpdate) {
          const statuses = ['applying', 'applied', 'interview', 'offer'] as const;

          // More realistic progression - not all jobs get offers
          let nextIndex: number;
          if (job.status === 'applying') {
            nextIndex = 1; // applying -> applied
          } else if (job.status === 'applied') {
            nextIndex = Math.random() > 0.7 ? 2 : 1; // 30% chance for interview
          } else if (job.status === 'interview') {
            nextIndex = Math.random() > 0.6 ? 3 : 2; // 40% chance for offer
          } else {
            nextIndex = 0; // reset to applying for demo
          }

          return { ...job, status: statuses[nextIndex] };
        }
        return job;
      });
    }, 2000); // Faster updates to show more activity

    return () => clearInterval(timer);
  });
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }
</style>

<section class="min-h-screen bg-gray-50">
  <div class="grid min-h-screen lg:grid-cols-[2fr_2fr]">
    <!-- Left side - Cleaner content layout (40%) -->
    <div class="relative flex flex-col justify-center overflow-hidden bg-white p-8 lg:p-12">
      <!-- Subtle floating design elements -->
      <div
        class="absolute right-4 top-16 h-20 w-20 rounded-full bg-gradient-to-br from-blue-50 to-indigo-50 opacity-40 blur-xl">
      </div>
      <div
        class="absolute bottom-32 left-4 h-16 w-16 rounded-full bg-gradient-to-br from-purple-50 to-pink-50 opacity-30 blur-lg">
      </div>

      <!-- Main content -->
      <div class="relative z-10 max-w-md space-y-8">
        <!-- Cleaner header section -->
        <div class="space-y-6">
          <!-- Simplified badge -->
          <div
            class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700">
            <Bot class="h-4 w-4" />
            <span>AI-Powered Automation</span>
          </div>

          <h1 class="text-4xl font-bold leading-tight text-gray-900 lg:text-5xl">
            Apply to
            <span class="relative text-blue-600">
              Hundreds
              <div
                class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60">
              </div>
            </span>
            of Jobs Automatically
          </h1>

          <p class="text-lg text-gray-600">
            Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.
          </p>
        </div>

        <!-- Streamlined key benefits -->
        <div class="space-y-3">
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-3 w-3 text-green-600" />
            </div>
            <span class="text-gray-700">100+ applications in minutes</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-3 w-3 text-blue-600" />
            </div>
            <span class="text-gray-700">AI-powered resume matching</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-3 w-3 text-purple-600" />
            </div>
            <span class="text-gray-700">Real-time tracking & analytics</span>
          </div>
        </div>

        <!-- Simplified CTA section -->
        <div class="space-y-4">
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              size="lg"
              class="group rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-3 font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <span class="flex items-center gap-2">
                Start Free Trial
                <ArrowUpRight class="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
            <button
              class="group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <Play class="h-4 w-4 group-hover:text-blue-600" />
              <span>Watch Demo</span>
            </button>
          </div>

          <div class="flex items-center gap-3 text-xs text-gray-500">
            <div class="flex items-center gap-1">
              <div class="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <span>•</span>
            <span>Setup in 2 minutes</span>
          </div>
        </div>

        <!-- Compact social proof -->
        <div class="rounded-lg bg-gray-50 p-4">
          <div class="mb-2 text-sm font-medium text-gray-900">Trusted by 10,000+ job seekers</div>
          <div class="flex items-center gap-6 text-xs">
            <div>
              <span class="font-bold text-gray-900">50K+</span>
              <span class="text-gray-500"> applications</span>
            </div>
            <div>
              <span class="font-bold text-gray-900">85%</span>
              <span class="text-gray-500"> success rate</span>
            </div>
            <div>
              <span class="font-bold text-gray-900">2.5x</span>
              <span class="text-gray-500"> faster</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Clean Job Application Stream (60%) -->
    <div class="relative overflow-hidden bg-slate-900">
      <!-- Subtle background pattern -->
      <div class="absolute inset-0">
        <div
          class="absolute inset-0 opacity-5"
          style="background-image: linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px); background-size: 40px 40px;">
        </div>
      </div>

      <!-- Main content area -->
      <div class="relative flex h-full flex-col justify-center p-8 lg:p-12">
        <!-- Enhanced Header with live stats -->
        <div class="mb-6">
          <div class="mb-4 flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="relative">
                <div class="h-3 w-3 animate-pulse rounded-full bg-green-400"></div>
                <div
                  class="absolute inset-0 h-3 w-3 animate-ping rounded-full bg-green-400 opacity-75">
                </div>
              </div>
              <span class="text-sm font-medium text-green-400">AI Agent Active</span>
              <div class="rounded-full bg-green-400/20 px-3 py-1 text-xs text-green-300">
                {jobCards.filter((j) => j.status === 'applying').length} processing
              </div>
              <div class="rounded-full bg-blue-400/20 px-3 py-1 text-xs text-blue-300">
                {jobCards.length} total jobs
              </div>
            </div>
            <div class="rounded-lg bg-white/10 px-3 py-1 text-xs text-blue-300 backdrop-blur-sm">
              Live Demo
            </div>
          </div>
          <h3 class="mb-2 text-3xl font-bold text-white">Mass Application Stream</h3>
          <p class="text-blue-200">
            Watch AI apply to hundreds of jobs simultaneously with smart matching
          </p>
        </div>

        <!-- Clean Job Cards Stream -->
        <div class="relative max-h-[500px] space-y-3 overflow-hidden">
          {#each jobCards.slice(0, 6) as job, index}
            <div
              class="transform transition-all duration-500 ease-out"
              style="transform: translateY({index * 1}px); opacity: {1 - index * 0.1};">
              <div
                class="hover:bg-white/8 group relative rounded-lg border border-white/10 bg-white/5 p-3 backdrop-blur-sm transition-all duration-300 hover:border-white/20">
                <!-- Simple active indicator -->
                {#if job.status === 'applying'}
                  <div class="absolute left-0 top-0 h-full w-1 rounded-l-lg bg-yellow-400"></div>
                {/if}

                <!-- Clean job card layout -->
                <div class="flex items-center gap-3">
                  <!-- Company logo -->
                  <div class="relative flex-shrink-0">
                    <div class="h-10 w-10 overflow-hidden rounded-lg bg-white p-1">
                      <img
                        src={job.logoUrl}
                        alt={job.company}
                        class="h-full w-full object-contain"
                        loading="lazy" />
                    </div>
                    <!-- Match score badge -->
                    <div
                      class="absolute -bottom-1 -right-1 rounded-full bg-green-500 px-1 py-0.5 text-xs font-bold text-white">
                      {job.matchScore}%
                    </div>
                  </div>

                  <!-- Job info -->
                  <div class="min-w-0 flex-1">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <div class="truncate text-sm font-medium text-white">{job.company}</div>
                        <div class="text-xs text-gray-400">{job.timeAgo}</div>
                      </div>

                      <!-- Status indicator -->
                      <div class="flex items-center gap-1.5">
                        {#if job.status === 'applying'}
                          <div class="h-1.5 w-1.5 animate-pulse rounded-full bg-yellow-400"></div>
                          <span class="text-xs text-yellow-400">Applying</span>
                        {:else if job.status === 'applied'}
                          <div class="h-1.5 w-1.5 rounded-full bg-blue-400"></div>
                          <span class="text-xs text-blue-400">Applied</span>
                        {:else if job.status === 'interview'}
                          <div class="h-1.5 w-1.5 animate-pulse rounded-full bg-purple-400"></div>
                          <span class="text-xs text-purple-400">Interview</span>
                        {:else if job.status === 'offer'}
                          <div class="h-1.5 w-1.5 animate-bounce rounded-full bg-green-400"></div>
                          <span class="text-xs text-green-400">Offer!</span>
                        {/if}
                      </div>
                    </div>

                    <div class="mt-0.5 truncate text-xs text-blue-200">{job.role}</div>

                    <!-- Job details -->
                    <div class="mt-2 flex items-center gap-3 text-xs text-gray-300">
                      <div class="flex items-center gap-1">
                        <DollarSign class="h-3 w-3" />
                        <span>{job.salary}</span>
                      </div>
                      <div class="flex items-center gap-1">
                        <MapPin class="h-3 w-3" />
                        <span class="truncate">{job.location}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {/each}

          <!-- Fade out gradient at bottom -->
          <div
            class="pointer-events-none absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-slate-900 to-transparent">
          </div>
        </div>

        <!-- Enhanced Bottom stats with better visual hierarchy -->
        <div class="mt-8 rounded-xl border border-white/20 bg-white/5 p-6 backdrop-blur-sm">
          <div class="mb-4 text-center">
            <h4 class="text-sm font-medium text-white">Today's Performance</h4>
            <p class="text-xs text-blue-300">Real-time automation metrics</p>
          </div>

          <div class="grid grid-cols-3 gap-6">
            <div class="text-center">
              <div class="mb-1 flex items-center justify-center">
                <div class="text-2xl font-bold text-white">
                  {jobCards.filter((j) => j.status === 'applied').length +
                    jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length}
                </div>
                <TrendingUp class="ml-1 h-4 w-4 text-green-400" />
              </div>
              <div class="text-xs font-medium text-blue-200">Applications Sent</div>
              <div class="text-xs text-gray-400">
                +{Math.floor(Math.random() * 5) + 2} this hour
              </div>
            </div>

            <div class="text-center">
              <div class="mb-1 flex items-center justify-center">
                <div class="text-2xl font-bold text-white">
                  {jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length}
                </div>
                <Eye class="ml-1 h-4 w-4 text-purple-400" />
              </div>
              <div class="text-xs font-medium text-blue-200">Responses</div>
              <div class="text-xs text-gray-400">{Math.floor(Math.random() * 3) + 1} pending</div>
            </div>

            <div class="text-center">
              <div class="mb-1 flex items-center justify-center">
                <div class="text-2xl font-bold text-white">
                  {jobCards.filter((j) => j.status === 'offer').length}
                </div>
                <Star class="ml-1 h-4 w-4 text-yellow-400" />
              </div>
              <div class="text-xs font-medium text-blue-200">Job Offers</div>
              <div class="text-xs text-gray-400">
                {jobCards.filter((j) => j.status === 'offer').length > 0
                  ? 'Congratulations!'
                  : 'Keep going!'}
              </div>
            </div>
          </div>

          <!-- Success rate indicator -->
          <div class="mt-4 border-t border-white/10 pt-4">
            <div class="flex items-center justify-between text-xs">
              <span class="text-blue-300">Success Rate</span>
              <span class="font-medium text-green-400">
                {Math.round(
                  ((jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length) /
                    Math.max(jobCards.filter((j) => j.status !== 'applying').length, 1)) *
                    100
                )}%
              </span>
            </div>
            <div class="mt-2 h-1.5 rounded-full bg-white/10">
              <div
                class="h-full rounded-full bg-gradient-to-r from-green-400 to-emerald-400 transition-all duration-500"
                style="width: {Math.round(
                  ((jobCards.filter((j) => j.status === 'interview').length +
                    jobCards.filter((j) => j.status === 'offer').length) /
                    Math.max(jobCards.filter((j) => j.status !== 'applying').length, 1)) *
                    100
                )}%">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
